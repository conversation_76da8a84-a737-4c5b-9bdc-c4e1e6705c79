<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, Link } from '@inertiajs/vue3';
import { ref, onMounted, computed } from 'vue';

const breadcrumbs = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Patients', href: '/patients' },
];

const loading = ref(false);
const patients = ref([]);
const searchQuery = ref('');
const selectedPatient = ref(null);
const showPatientModal = ref(false);

const fetchPatients = async () => {
    loading.value = true;
    try {
        const response = await window.axios.get('/patients-list');
        // Handle paginated response - extract the data array
        patients.value = response.data.data || response.data || [];
    } catch (error) {
        console.error('Error fetching patients:', error);
        patients.value = [];
    } finally {
        loading.value = false;
    }
};

// Helper functions
const calculateAge = (dateOfBirth) => {
    if (!dateOfBirth) return 'N/A';
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
    }
    return age;
};

const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
};

const parseJsonField = (field) => {
    if (!field) return [];
    if (typeof field === 'string') {
        try {
            return JSON.parse(field);
        } catch (e) {
            return [];
        }
    }
    return Array.isArray(field) ? field : [];
};

const parseJsonObject = (field) => {
    if (!field) return {};
    if (typeof field === 'string') {
        try {
            return JSON.parse(field);
        } catch (e) {
            return {};
        }
    }
    return typeof field === 'object' ? field : {};
};

const viewPatientDetails = (patient) => {
    selectedPatient.value = patient;
    showPatientModal.value = true;
};

const closeModal = () => {
    showPatientModal.value = false;
    selectedPatient.value = null;
};

// Computed properties
const filteredPatients = computed(() => {
    if (!searchQuery.value) return patients.value;
    return patients.value.filter(patient =>
        patient.user?.name?.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        patient.user?.email?.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        patient.user?.phone_number?.includes(searchQuery.value)
    );
});

onMounted(() => {
    fetchPatients();
});
</script>

<template>
    <Head title="Patient Management" />

    <AppLayout>
        <template #header>
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200">
                        Patient Management
                    </h2>
                    <nav class="flex mt-2" aria-label="Breadcrumb">
                        <ol class="inline-flex items-center space-x-1 md:space-x-3">
                            <li v-for="(breadcrumb, index) in breadcrumbs" :key="index" class="inline-flex items-center">
                                <Link v-if="index < breadcrumbs.length - 1" 
                                    :href="breadcrumb.href" 
                                    class="text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                                    {{ breadcrumb.title }}
                                </Link>
                                <span v-else class="text-sm font-medium text-gray-700 dark:text-gray-400">
                                    {{ breadcrumb.title }}
                                </span>
                                <svg v-if="index < breadcrumbs.length - 1" class="w-3 h-3 mx-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </li>
                        </ol>
                    </nav>
                </div>
                <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Add Patient
                </button>
            </div>
        </template>

        <div class="py-12">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                <!-- Search and Filter Bar -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6">
                        <div class="flex flex-col sm:flex-row gap-4">
                            <div class="flex-1">
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-search text-gray-400"></i>
                                    </div>
                                    <input
                                        v-model="searchQuery"
                                        type="text"
                                        placeholder="Search patients by name, email, or phone..."
                                        class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                    >
                                </div>
                            </div>
                            <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                <i class="fas fa-plus mr-2"></i>Add Patient
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-users text-2xl text-green-500"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Patients</p>
                                    <p class="text-2xl font-semibold text-gray-900 dark:text-gray-100">{{ patients.length }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-user-check text-2xl text-blue-500"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Active Patients</p>
                                    <p class="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                                        {{ patients.filter(p => p.user?.is_active).length }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-shield-alt text-2xl text-purple-500"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">With Insurance</p>
                                    <p class="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                                        {{ patients.filter(p => p.insurance_provider).length }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-exclamation-triangle text-2xl text-orange-500"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">With Allergies</p>
                                    <p class="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                                        {{ patients.filter(p => parseJsonField(p.allergies).length > 0).length }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Patients Table -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900 dark:text-gray-100">
                        <div v-if="loading" class="text-center py-8">
                            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                        </div>

                        <div v-else-if="filteredPatients.length === 0" class="text-center py-8">
                            <i class="fas fa-users text-4xl text-gray-400 mb-4"></i>
                            <p class="text-gray-500">No patients found</p>
                        </div>

                        <div v-else class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                <thead class="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Patient
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Contact
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Age/Gender
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Insurance
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Medical Info
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                    <tr v-for="patient in filteredPatients" :key="patient.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    <div class="h-10 w-10 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
                                                        <i class="fas fa-user text-green-600 dark:text-green-400"></i>
                                                    </div>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                        {{ patient.user?.name || 'No Name' }}
                                                    </div>
                                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                                        ID: {{ patient.id || 'No ID' }}
                                                    </div>
                                                    <div class="text-xs text-gray-400">
                                                        User ID: {{ patient.user_id || 'No User ID' }}
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900 dark:text-gray-100">{{ patient.user?.email || 'No Email' }}</div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">{{ patient.user?.phone_number || 'No Phone' }}</div>
                                            <div class="text-xs text-gray-400">
                                                <span :class="patient.user?.is_active ? 'text-green-600' : 'text-red-600'">
                                                    {{ patient.user?.is_active ? 'Active' : 'Inactive' }}
                                                </span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900 dark:text-gray-100">{{ calculateAge(patient.date_of_birth) }} years</div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400 capitalize">{{ patient.gender || 'N/A' }}</div>
                                            <div class="text-xs text-gray-400">{{ formatDate(patient.date_of_birth) }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900 dark:text-gray-100">{{ patient.insurance_provider || 'None' }}</div>
                                            <div class="text-xs text-gray-500 dark:text-gray-400">{{ patient.insurance_policy_number || 'N/A' }}</div>
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="text-xs space-y-1">
                                                <div v-if="parseJsonField(patient.allergies).length > 0" class="flex items-center">
                                                    <i class="fas fa-exclamation-triangle text-red-500 mr-1"></i>
                                                    <span class="text-red-600">{{ parseJsonField(patient.allergies).length }} allergies</span>
                                                </div>
                                                <div v-if="parseJsonField(patient.medications).length > 0" class="flex items-center">
                                                    <i class="fas fa-pills text-blue-500 mr-1"></i>
                                                    <span class="text-blue-600">{{ parseJsonField(patient.medications).length }} medications</span>
                                                </div>
                                                <div v-if="parseJsonObject(patient.health_history).chronic_conditions?.length > 0" class="flex items-center">
                                                    <i class="fas fa-heartbeat text-orange-500 mr-1"></i>
                                                    <span class="text-orange-600">{{ parseJsonObject(patient.health_history).chronic_conditions?.length }} conditions</span>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button @click="viewPatientDetails(patient)" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3">
                                                <i class="fas fa-eye mr-1"></i>View
                                            </button>
                                            <button class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3">
                                                <i class="fas fa-edit mr-1"></i>Edit
                                            </button>
                                            <button class="text-purple-600 hover:text-purple-900 dark:text-purple-400 dark:hover:text-purple-300">
                                                <i class="fas fa-calendar mr-1"></i>Schedule
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Patient Details Modal -->
        <div v-if="showPatientModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-2/3 shadow-lg rounded-md bg-white dark:bg-gray-800">
                <div class="mt-3">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                            Patient Details - {{ selectedPatient?.user?.name }}
                        </h3>
                        <button @click="closeModal" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>

                    <div v-if="selectedPatient" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Personal Information -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="text-md font-semibold text-gray-900 dark:text-gray-100 mb-3">
                                <i class="fas fa-user mr-2"></i>Personal Information
                            </h4>
                            <div class="space-y-2 text-sm">
                                <div><strong>Name:</strong> {{ selectedPatient.user?.name }}</div>
                                <div><strong>Email:</strong> {{ selectedPatient.user?.email }}</div>
                                <div><strong>Phone:</strong> {{ selectedPatient.user?.phone_number || 'N/A' }}</div>
                                <div><strong>Date of Birth:</strong> {{ formatDate(selectedPatient.date_of_birth) }}</div>
                                <div><strong>Age:</strong> {{ calculateAge(selectedPatient.date_of_birth) }} years</div>
                                <div><strong>Gender:</strong> {{ selectedPatient.gender || 'N/A' }}</div>
                                <div><strong>Status:</strong>
                                    <span :class="selectedPatient.user?.is_active ? 'text-green-600' : 'text-red-600'">
                                        {{ selectedPatient.user?.is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Insurance Information -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="text-md font-semibold text-gray-900 dark:text-gray-100 mb-3">
                                <i class="fas fa-shield-alt mr-2"></i>Insurance Information
                            </h4>
                            <div class="space-y-2 text-sm">
                                <div><strong>Provider:</strong> {{ selectedPatient.insurance_provider || 'None' }}</div>
                                <div><strong>Policy Number:</strong> {{ selectedPatient.insurance_policy_number || 'N/A' }}</div>
                                <div><strong>Expiry Date:</strong> {{ formatDate(selectedPatient.insurance_expiry_date) }}</div>
                            </div>
                        </div>

                        <!-- Emergency Contact -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="text-md font-semibold text-gray-900 dark:text-gray-100 mb-3">
                                <i class="fas fa-phone mr-2"></i>Emergency Contact
                            </h4>
                            <div class="space-y-2 text-sm">
                                <div><strong>Name:</strong> {{ selectedPatient.emergency_contact_name || 'N/A' }}</div>
                                <div><strong>Phone:</strong> {{ selectedPatient.emergency_contact_phone || 'N/A' }}</div>
                                <div><strong>Relationship:</strong> {{ selectedPatient.emergency_contact_relationship || 'N/A' }}</div>
                            </div>
                        </div>

                        <!-- Medical Information -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="text-md font-semibold text-gray-900 dark:text-gray-100 mb-3">
                                <i class="fas fa-heartbeat mr-2"></i>Medical Information
                            </h4>
                            <div class="space-y-3 text-sm">
                                <!-- Allergies -->
                                <div>
                                    <strong class="text-red-600">Allergies:</strong>
                                    <div v-if="parseJsonField(selectedPatient.allergies).length > 0" class="mt-1">
                                        <span v-for="allergy in parseJsonField(selectedPatient.allergies)" :key="allergy"
                                              class="inline-block bg-red-100 text-red-800 text-xs px-2 py-1 rounded mr-1 mb-1">
                                            {{ allergy }}
                                        </span>
                                    </div>
                                    <span v-else class="text-gray-500">None known</span>
                                </div>

                                <!-- Medications -->
                                <div>
                                    <strong class="text-blue-600">Current Medications:</strong>
                                    <div v-if="parseJsonField(selectedPatient.medications).length > 0" class="mt-1">
                                        <div v-for="medication in parseJsonField(selectedPatient.medications)" :key="medication"
                                             class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded mb-1 inline-block mr-1">
                                            {{ medication }}
                                        </div>
                                    </div>
                                    <span v-else class="text-gray-500">None</span>
                                </div>

                                <!-- Chronic Conditions -->
                                <div>
                                    <strong class="text-orange-600">Chronic Conditions:</strong>
                                    <div v-if="parseJsonObject(selectedPatient.health_history).chronic_conditions?.length > 0" class="mt-1">
                                        <span v-for="condition in parseJsonObject(selectedPatient.health_history).chronic_conditions" :key="condition"
                                              class="inline-block bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded mr-1 mb-1">
                                            {{ condition }}
                                        </span>
                                    </div>
                                    <span v-else class="text-gray-500">None</span>
                                </div>

                                <!-- Surgeries -->
                                <div>
                                    <strong class="text-purple-600">Previous Surgeries:</strong>
                                    <div v-if="parseJsonObject(selectedPatient.health_history).surgeries?.length > 0" class="mt-1">
                                        <div v-for="surgery in parseJsonObject(selectedPatient.health_history).surgeries" :key="surgery"
                                             class="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded mb-1 inline-block mr-1">
                                            {{ surgery }}
                                        </div>
                                    </div>
                                    <span v-else class="text-gray-500">None</span>
                                </div>
                            </div>
                        </div>

                        <!-- Appointment Preferences -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg md:col-span-2">
                            <h4 class="text-md font-semibold text-gray-900 dark:text-gray-100 mb-3">
                                <i class="fas fa-calendar mr-2"></i>Appointment Preferences
                            </h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div>
                                    <strong>Preferred Days:</strong>
                                    <div v-if="parseJsonObject(selectedPatient.appointment_preferences).preferred_days?.length > 0" class="mt-1">
                                        <span v-for="day in parseJsonObject(selectedPatient.appointment_preferences).preferred_days" :key="day"
                                              class="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded mr-1 mb-1">
                                            {{ day }}
                                        </span>
                                    </div>
                                    <span v-else class="text-gray-500">No preference</span>
                                </div>
                                <div>
                                    <strong>Preferred Time:</strong>
                                    <span class="ml-2">{{ parseJsonObject(selectedPatient.appointment_preferences).preferred_time || 'No preference' }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end mt-6 space-x-3">
                        <button @click="closeModal" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                            Close
                        </button>
                        <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            <i class="fas fa-edit mr-2"></i>Edit Patient
                        </button>
                        <button class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                            <i class="fas fa-calendar-plus mr-2"></i>Schedule Appointment
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
